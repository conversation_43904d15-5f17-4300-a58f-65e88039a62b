import { pgTable, text, timestamp, integer, decimal, boolean, uuid, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Trainers table
export const trainers = pgTable('trainers', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  phone: text('phone'),
  emailVerified: boolean('email_verified').default(false),
  image: text('image'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Customers table
export const customers = pgTable('customers', {
  id: uuid('id').defaultRandom().primaryKey(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  name: text('name').notNull(),
  email: text('email'),
  phone: text('phone'),
  parentName: text('parent_name'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Packages table - admin-defined session packages
export const packages = pgTable('packages', {
  id: uuid('id').defaultRandom().primaryKey(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  name: text('name').notNull(),
  description: text('description'),
  sessionCount: integer('session_count').notNull(),
  price: decimal('price', { precision: 10, scale: 2 }).notNull(),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Customer packages table - tracks packages assigned to customers
export const customerPackages = pgTable('customer_packages', {
  id: uuid('id').defaultRandom().primaryKey(),
  customerId: uuid('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  packageId: uuid('package_id')
    .references(() => packages.id)
    .notNull(),
  sessionsRemaining: integer('sessions_remaining').notNull(),
  purchaseDate: timestamp('purchase_date').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Customer purchases table
export const customerPurchases = pgTable('customer_purchases', {
  id: uuid('id').defaultRandom().primaryKey(),
  customerId: uuid('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  packageId: uuid('package_id').references(() => packages.id), // nullable for backward compatibility with existing purchases
  sessionsPurchased: integer('sessions_purchased').notNull(),
  amountPaid: decimal('amount_paid', { precision: 10, scale: 2 }).notNull(),
  purchaseDate: timestamp('purchase_date').defaultNow().notNull(),
  paymentStatus: text('payment_status').default('completed').notNull(), // completed, pending, failed
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Credit transactions table for audit trail
export const creditTransactions = pgTable('credit_transactions', {
  id: uuid('id').defaultRandom().primaryKey(),
  customerId: uuid('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  type: text('type').notNull(), // purchase, deduction, refund, adjustment
  amount: integer('amount').notNull(), // positive for credits added, negative for credits deducted
  balanceBefore: integer('balance_before').notNull(),
  balanceAfter: integer('balance_after').notNull(),
  description: text('description').notNull(),
  relatedPurchaseId: uuid('related_purchase_id').references(() => customerPurchases.id),
  relatedWorkoutId: uuid('related_workout_id').references(() => workouts.id),
  relatedParticipantId: uuid('related_participant_id').references(() => workoutParticipants.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Workouts table
export const workouts = pgTable('workouts', {
  id: uuid('id').defaultRandom().primaryKey(),
  trainerId: uuid('trainer_id')
    .references(() => trainers.id)
    .notNull(),
  title: text('title').notNull(),
  description: text('description'),
  startTime: timestamp('start_time').notNull(),
  endTime: timestamp('end_time').notNull(),
  minParticipants: integer('min_participants').default(3).notNull(),
  maxParticipants: integer('max_participants').default(5).notNull(),
  status: text('status').default('scheduled').notNull(), // scheduled, confirmed, completed, cancelled
  location: text('location'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Workout participants table (many-to-many relationship)
export const workoutParticipants = pgTable('workout_participants', {
  id: uuid('id').defaultRandom().primaryKey(),
  workoutId: uuid('workout_id')
    .references(() => workouts.id)
    .notNull(),
  customerId: uuid('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  status: text('status').default('enrolled').notNull(), // enrolled, confirmed, cancelled
  enrolledAt: timestamp('enrolled_at').defaultNow().notNull(),
  confirmedAt: timestamp('confirmed_at'),
  creditDeducted: boolean('credit_deducted').default(false).notNull(),
});

// Better Auth tables
export const user = pgTable('user', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  emailVerified: boolean('email_verified')
    .$defaultFn(() => false)
    .notNull(),
  image: text('image'),
  createdAt: timestamp('created_at')
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
  updatedAt: timestamp('updated_at')
    .$defaultFn(() => /* @__PURE__ */ new Date())
    .notNull(),
  role: text('role').default('trainer'),
});

export const session = pgTable('session', {
  id: text('id').primaryKey(),
  expiresAt: timestamp('expires_at').notNull(),
  token: text('token').notNull().unique(),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  userId: text('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
});

export const account = pgTable('account', {
  id: text('id').primaryKey(),
  accountId: text('account_id').notNull(),
  providerId: text('provider_id').notNull(),
  userId: text('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  idToken: text('id_token'),
  accessTokenExpiresAt: timestamp('access_token_expires_at'),
  refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
  scope: text('scope'),
  password: text('password'),
  createdAt: timestamp('created_at').notNull(),
  updatedAt: timestamp('updated_at').notNull(),
});

export const verification = pgTable('verification', {
  id: text('id').primaryKey(),
  identifier: text('identifier').notNull(),
  value: text('value').notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()),
  updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date()),
});

// Relations
export const trainersRelations = relations(trainers, ({ many }) => ({
  customers: many(customers),
  workouts: many(workouts),
  packages: many(packages),
}));

export const customersRelations = relations(customers, ({ one, many }) => ({
  trainer: one(trainers, {
    fields: [customers.trainerId],
    references: [trainers.id],
  }),
  purchases: many(customerPurchases),
  customerPackages: many(customerPackages),
  workoutParticipants: many(workoutParticipants),
  creditTransactions: many(creditTransactions),
}));

export const packagesRelations = relations(packages, ({ one, many }) => ({
  trainer: one(trainers, {
    fields: [packages.trainerId],
    references: [trainers.id],
  }),
  customerPackages: many(customerPackages),
  purchases: many(customerPurchases),
}));

export const customerPackagesRelations = relations(customerPackages, ({ one }) => ({
  customer: one(customers, {
    fields: [customerPackages.customerId],
    references: [customers.id],
  }),
  package: one(packages, {
    fields: [customerPackages.packageId],
    references: [packages.id],
  }),
}));

export const customerPurchasesRelations = relations(customerPurchases, ({ one }) => ({
  customer: one(customers, {
    fields: [customerPurchases.customerId],
    references: [customers.id],
  }),
  package: one(packages, {
    fields: [customerPurchases.packageId],
    references: [packages.id],
  }),
}));

export const creditTransactionsRelations = relations(creditTransactions, ({ one }) => ({
  customer: one(customers, {
    fields: [creditTransactions.customerId],
    references: [customers.id],
  }),
  relatedPurchase: one(customerPurchases, {
    fields: [creditTransactions.relatedPurchaseId],
    references: [customerPurchases.id],
  }),
  relatedWorkout: one(workouts, {
    fields: [creditTransactions.relatedWorkoutId],
    references: [workouts.id],
  }),
  relatedParticipant: one(workoutParticipants, {
    fields: [creditTransactions.relatedParticipantId],
    references: [workoutParticipants.id],
  }),
}));

export const workoutsRelations = relations(workouts, ({ one, many }) => ({
  trainer: one(trainers, {
    fields: [workouts.trainerId],
    references: [trainers.id],
  }),
  participants: many(workoutParticipants),
}));

export const workoutParticipantsRelations = relations(workoutParticipants, ({ one }) => ({
  workout: one(workouts, {
    fields: [workoutParticipants.workoutId],
    references: [workouts.id],
  }),
  customer: one(customers, {
    fields: [workoutParticipants.customerId],
    references: [customers.id],
  }),
}));

export type Trainer = typeof trainers.$inferSelect;
export type NewTrainer = typeof trainers.$inferInsert;
export type Customer = typeof customers.$inferSelect;
export type NewCustomer = typeof customers.$inferInsert;
export type Package = typeof packages.$inferSelect;
export type NewPackage = typeof packages.$inferInsert;
export type CustomerPackage = typeof customerPackages.$inferSelect;
export type NewCustomerPackage = typeof customerPackages.$inferInsert;
export type CustomerPurchase = typeof customerPurchases.$inferSelect;
export type NewCustomerPurchase = typeof customerPurchases.$inferInsert;
export type CreditTransaction = typeof creditTransactions.$inferSelect;
export type NewCreditTransaction = typeof creditTransactions.$inferInsert;
export type Workout = typeof workouts.$inferSelect;
export type NewWorkout = typeof workouts.$inferInsert;
export type WorkoutParticipant = typeof workoutParticipants.$inferSelect;
export type NewWorkoutParticipant = typeof workoutParticipants.$inferInsert;
