import { db, customers, packages, customerPackages } from '@workspace/auth/server';
import { eq, and, gt } from 'drizzle-orm';

/**
 * Migrates existing customer session credits to package-based system
 * Creates a "Legacy Sessions" package for each trainer and assigns existing credits
 */
export async function migrateCustomerCreditsToPackages() {
  console.log('Starting migration of customer credits to packages...');

  return await db.transaction(async (tx) => {
    // Get all customers with session credits > 0
    const customersWithCredits = await tx
      .select({
        id: customers.id,
        trainerId: customers.trainerId,
        name: customers.name,
        sessionCredits: customers.sessionCredits,
      })
      .from(customers)
      .where(gt(customers.sessionCredits, 0));

    console.log(`Found ${customersWithCredits.length} customers with session credits`);

    if (customersWithCredits.length === 0) {
      console.log('No customers with credits found. Migration complete.');
      return { migrated: 0, packagesCreated: 0 };
    }

    // Group customers by trainer to create legacy packages
    const trainerGroups = customersWithCredits.reduce((groups, customer) => {
      if (!groups[customer.trainerId]) {
        groups[customer.trainerId] = [];
      }
      groups[customer.trainerId]!.push(customer);
      return groups;
    }, {} as Record<string, typeof customersWithCredits>);

    let packagesCreated = 0;
    let customersProcessed = 0;

    // Create legacy packages for each trainer and migrate customers
    for (const [trainerId, trainerCustomers] of Object.entries(trainerGroups)) {
      console.log(`Processing trainer ${trainerId} with ${trainerCustomers.length} customers`);

      // Check if legacy package already exists for this trainer
      let legacyPackage = await tx
        .select()
        .from(packages)
        .where(and(eq(packages.trainerId, trainerId), eq(packages.name, 'Legacy Sessions')))
        .limit(1);

      if (legacyPackage.length === 0) {
        // Create legacy package for this trainer
        const [newLegacyPackage] = await tx
          .insert(packages)
          .values({
            trainerId,
            name: 'Legacy Sessions',
            description: 'Migrated sessions from previous system',
            sessionCount: 1, // Individual sessions
            price: '0.00', // No price for legacy sessions
            isActive: false, // Inactive so it can't be assigned to new customers
          })
          .returning();

        if (!newLegacyPackage) {
          throw new Error('Failed to create new legacy package');
        }
        legacyPackage = [newLegacyPackage];
        packagesCreated++;
        console.log(`Created legacy package for trainer ${trainerId}`);
      }

      const legacyPackageId = legacyPackage[0]?.id;

      if (!legacyPackageId) {
        console.warn(`Could not find or create legacy package for trainer ${trainerId}. Skipping migration for their customers.`);
        continue;
      }

      // Migrate each customer's credits
      for (const customer of trainerCustomers) {
        if (customer.sessionCredits > 0) {
          // Check if customer already has a legacy package assignment
          const existingAssignment = await tx
            .select()
            .from(customerPackages)
            .where(
              and(
                eq(customerPackages.customerId, customer.id),
                eq(customerPackages.packageId, legacyPackageId)
              )
            )
            .limit(1);

          if (existingAssignment.length === 0) {
            // Create customer package assignment
            await tx
              .insert(customerPackages)
              .values({
                customerId: customer.id,
                packageId: legacyPackageId,
                sessionsRemaining: customer.sessionCredits,
                purchaseDate: new Date(), // Use current date as purchase date
              });

            console.log(`Migrated ${customer.sessionCredits} credits for customer ${customer.name}`);
            customersProcessed++;
          } else {
            console.log(`Customer ${customer.name} already has legacy package assignment, skipping`);
          }
        }
      }
    }

    console.log(`Migration complete. Created ${packagesCreated} legacy packages, processed ${customersProcessed} customers`);
    
    return {
      migrated: customersProcessed,
      packagesCreated,
    };
  });
}

/**
 * Verifies the migration by checking that all customers' total package sessions
 * match their original session credits
 */
export async function verifyMigration() {
  console.log('Verifying migration...');

  const customersWithCredits = await db
    .select({
      id: customers.id,
      name: customers.name,
      sessionCredits: customers.sessionCredits,
    })
    .from(customers)
    .where(gt(customers.sessionCredits, 0));

  let verificationErrors = 0;

  for (const customer of customersWithCredits) {
    // Calculate total sessions from packages
    const customerPackagesList = await db
      .select({
        sessionsRemaining: customerPackages.sessionsRemaining,
      })
      .from(customerPackages)
      .where(eq(customerPackages.customerId, customer.id));

    const totalPackageSessions = customerPackagesList.reduce(
      (sum, pkg) => sum + pkg.sessionsRemaining,
      0
    );

    if (totalPackageSessions !== customer.sessionCredits) {
      console.error(
        `Verification failed for customer ${customer.name}: ` +
        `sessionCredits=${customer.sessionCredits}, packageSessions=${totalPackageSessions}`
      );
      verificationErrors++;
    }
  }

  if (verificationErrors === 0) {
    console.log('Migration verification passed! All customer credits match package sessions.');
  } else {
    console.error(`Migration verification failed with ${verificationErrors} errors.`);
  }

  return verificationErrors === 0;
}

/**
 * Rollback migration by removing legacy packages and customer package assignments
 * WARNING: This will permanently delete migration data
 */
export async function rollbackMigration() {
  console.log('Rolling back migration...');

  return await db.transaction(async (tx) => {
    // Get all legacy packages
    const legacyPackages = await tx
      .select({ id: packages.id })
      .from(packages)
      .where(eq(packages.name, 'Legacy Sessions'));

    if (legacyPackages.length === 0) {
      console.log('No legacy packages found. Nothing to rollback.');
      return { deletedAssignments: 0, deletedPackages: 0 };
    }

    const legacyPackageIds = legacyPackages.map(pkg => pkg.id);

    // Delete customer package assignments for legacy packages
    let deletedAssignments = 0;
    for (const packageId of legacyPackageIds) {
      const result = await tx
        .delete(customerPackages)
        .where(eq(customerPackages.packageId, packageId))
        .returning();
      
      deletedAssignments += result.length || 0;
    }

    // Delete legacy packages
    let deletedPackages = 0;
    for (const packageId of legacyPackageIds) {
      const result = await tx
        .delete(packages)
        .where(eq(packages.id, packageId))
        .returning();
      
      deletedPackages += result.length || 0;
    }

    console.log(`Rollback complete. Deleted ${deletedAssignments} assignments and ${deletedPackages} packages.`);
    
    return {
      deletedAssignments,
      deletedPackages,
    };
  });
}
