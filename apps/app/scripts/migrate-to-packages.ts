#!/usr/bin/env tsx

/**
 * Migration script to convert existing customer session credits to package-based system
 * 
 * Usage:
 *   npx tsx scripts/migrate-to-packages.ts [--verify] [--rollback]
 * 
 * Options:
 *   --verify    Only verify the migration without making changes
 *   --rollback  Rollback the migration (WARNING: destructive)
 */

import { migrateCustomerCreditsToPackages, verifyMigration, rollbackMigration } from '../lib/migration-service';

async function main() {
  const args = process.argv.slice(2);
  const isVerifyOnly = args.includes('--verify');
  const isRollback = args.includes('--rollback');

  try {
    if (isRollback) {
      console.log('⚠️  WARNING: This will permanently delete migration data!');
      console.log('Press Ctrl+C to cancel, or wait 5 seconds to continue...');
      
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const result = await rollbackMigration();
      console.log('✅ Rollback completed:', result);
      
    } else if (isVerifyOnly) {
      console.log('🔍 Verifying migration...');
      const isValid = await verifyMigration();
      
      if (isValid) {
        console.log('✅ Migration verification passed!');
        process.exit(0);
      } else {
        console.log('❌ Migration verification failed!');
        process.exit(1);
      }
      
    } else {
      console.log('🚀 Starting migration to package-based system...');
      
      // Run migration
      const result = await migrateCustomerCreditsToPackages();
      console.log('✅ Migration completed:', result);
      
      // Verify migration
      console.log('🔍 Verifying migration...');
      const isValid = await verifyMigration();
      
      if (isValid) {
        console.log('✅ Migration verification passed!');
        console.log('🎉 Package-based system is ready to use!');
      } else {
        console.log('❌ Migration verification failed!');
        console.log('Please check the logs and consider rolling back.');
        process.exit(1);
      }
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n⚠️  Migration interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Migration terminated');
  process.exit(1);
});

main();
