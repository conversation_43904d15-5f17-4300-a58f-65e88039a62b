'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { updateCustomerSchema, type UpdateCustomerInput, type CustomerResponse } from '@/lib/validations';
import { updateCustomer } from '@/hooks/use-customers';

interface EditCustomerDialogProps {
  customer: CustomerResponse | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCustomerChanged?: () => void;
}

export function EditCustomerDialog({ customer, open, onOpenChange, onCustomerChanged }: EditCustomerDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UpdateCustomerInput>({
    resolver: zodResolver(updateCustomerSchema),
  });

  useEffect(() => {
    if (customer) {
      reset({
        name: customer.name,
        email: customer.email || '',
        phone: customer.phone || '',
        sessionCredits: customer.sessionCredits,
        parentName: customer?.parentName || '',
      });
    }
  }, [customer, reset]);

  const onSubmit = async (data: UpdateCustomerInput) => {
    if (!customer) return;

    setIsSubmitting(true);
    try {
      const updatedCustomer = await updateCustomer(customer.id, data);
      if (updatedCustomer) {
        toast.success('Customer updated successfully');
        onOpenChange(false);
        if (onCustomerChanged) {
          onCustomerChanged();
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update customer';
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isSubmitting) {
      onOpenChange(newOpen);
    }
  };

  if (!customer) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Customer</DialogTitle>
          <DialogDescription>
            Update customer information. Use the purchase dialog to manage session packages.
          </DialogDescription>
        </DialogHeader>

        <div className="mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Current Credits:</span>
            <Badge variant={customer.sessionCredits > 0 ? 'default' : 'secondary'}>
              {customer.sessionCredits} credits
            </Badge>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input id="name" {...register('name')} placeholder="Enter customer name" disabled={isSubmitting} />
            {errors.name && <p className="text-sm text-destructive">{errors.name.message}</p>}
          </div>

          {/* <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              placeholder="Enter email address"
              disabled={isSubmitting}
            />
            {errors.email && <p className="text-sm text-destructive">{errors.email.message}</p>}
          </div> */}

          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input id="phone" {...register('phone')} placeholder="Enter phone number" disabled={isSubmitting} />
            {errors.phone && <p className="text-sm text-destructive">{errors.phone.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="parentName">Parent&apos;s Name</Label>
            <Input
              id="parentName"
              {...register('parentName')}
              placeholder="Enter parent's name (optional)"
              disabled={isSubmitting}
            />
            {errors.parentName && <p className="text-sm text-destructive">{errors.parentName.message}</p>}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => handleOpenChange(false)} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update Customer'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
