'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import useSWRInfinite from 'swr/infinite';
import { getPurchasesKey } from '@/hooks/use-purchases';
import { fetcher } from '@/lib/fetcher';

interface PurchaseHistoryProps {
  customerId: string;
}

export function PurchaseHistory({ customerId }: PurchaseHistoryProps) {
  const PAGE_SIZE = 20;
  const getKey = (pageIndex: number, previousPageData: any) => {
    if (!customerId) return null;
    if (previousPageData && !previousPageData.data.length) return null;
    return getPurchasesKey(customerId, { limit: PAGE_SIZE, offset: pageIndex * PAGE_SIZE });
  };

  const { data, error, size, setSize, isLoading, isValidating, mutate } = useSWRInfinite(getKey, fetcher);

  const purchases = data ? data.flatMap((page: any) => page.data) : [];
  const pagination = data && data.length > 0 ? data[data.length - 1].pagination : { hasMore: false };
  const loading = isLoading || isValidating;

  if (loading && purchases.length === 0) {
    return <PurchaseHistorySkeleton />;
  }

  if (error) {
    return (
      <div className="text-center text-muted-foreground">
        <p>Error loading purchase history: {error.message || error}</p>
        <Button variant="outline" onClick={() => mutate()} className="mt-2">
          Try Again
        </Button>
      </div>
    );
  }

  if (purchases.length === 0) {
    return (
      <div className="text-center text-muted-foreground">
        <p>No purchase history found.</p>
        <p className="text-sm">Record the first purchase to get started.</p>
      </div>
    );
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'pending':
        return 'secondary';
      case 'failed':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground">
        {pagination.total} purchase{pagination.total !== 1 ? 's' : ''} found
      </div>

      <div className="space-y-3 max-h-96 overflow-y-auto">
        {purchases.map((purchase) => (
          <Card key={purchase.id}>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {purchase.sessionsPurchased} session{purchase.sessionsPurchased !== 1 ? 's' : ''}
                    </span>
                    <Badge className="capitalize" variant={getStatusBadgeVariant(purchase.paymentStatus)}>
                      {purchase.paymentStatus}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(purchase.purchaseDate).toLocaleDateString()} at{' '}
                    {new Date(purchase.purchaseDate).toLocaleTimeString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">${purchase.amountPaid}</div>
                  <div className="text-sm text-muted-foreground">
                    ${(parseFloat(purchase.amountPaid) / purchase.sessionsPurchased).toFixed(2)}/session
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {pagination.hasMore && (
        <div className="mt-4 text-center">
          <Button variant="outline" onClick={() => setSize(size + 1)} disabled={loading}>
            {loading ? 'Loading...' : 'Load More'}
          </Button>
        </div>
      )}
    </div>
  );
}

function PurchaseHistorySkeleton() {
  return (
    <div className="space-y-4 pt-8">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-5 w-16" />
            </div>
            <Skeleton className="h-3 w-32" />
          </div>
          <div className="text-right space-y-2">
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
      ))}
    </div>
  );
}
