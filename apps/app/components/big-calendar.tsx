'use client';

import { useState, useMemo } from 'react';
import { startOfWeek, endOfWeek } from 'date-fns';
import { useCalendarContext } from '@/components/event-calendar/calendar-context';
import { useWorkoutsList, createWorkout, updateWorkout, deleteWorkout, addParticipant } from '@/hooks/use-workouts';
import { CustomerSidebar } from '@/components/customers/customer-sidebar';
import { WorkoutDetailsModal } from '@/components/workouts/workout-details-modal';
import { WorkoutEditModal } from '@/components/workouts/workout-edit-modal';

import {
  EventCalendar,
  type CalendarEvent,
  // type EventColor,
  workoutToCalendarEvent,
  CalendarDndProvider,
  EventGap,
  EventHeight,
  WeekCellsHeight,
} from '@/components/event-calendar';
import type { CustomerResponse } from '@/lib/validations';
import { hasCustomerConflict, hasWorkoutTimeConflict, formatConflictMessage } from '@/lib/workout-utils';
import { toast } from 'sonner';
import { SidebarTrigger, useSidebar } from './ui/sidebar';
import { cn } from '@/lib/utils';

// Etiquettes data for calendar filtering
export const etiquettes = [
  {
    id: 'scheduled',
    name: 'Scheduled',
    color: 'blue',
    isActive: true,
  },
  {
    id: 'confirmed',
    name: 'Confirmed',
    color: 'violet',
    isActive: true,
  },
  {
    id: 'cancelled',
    name: 'Cancelled',
    color: 'rose',
    isActive: true,
  },
  {
    id: 'completed',
    name: 'Completed',
    color: 'emerald',
    isActive: true,
  },
];

export default function Component() {
  const { currentDate, isColorVisible } = useCalendarContext();
  const [selectedWorkoutId, setSelectedWorkoutId] = useState<string | null>(null);
  const [workoutModalOpen, setWorkoutModalOpen] = useState(false);
  const [editWorkout, setEditWorkout] = useState<any | null>(null);
  const [workoutEditModalOpen, setWorkoutEditModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Calculate date range for fetching workouts (current week)
  const startDate = startOfWeek(currentDate, { weekStartsOn: 0 });
  const endDate = endOfWeek(currentDate, { weekStartsOn: 0 });

  // Fetch workouts for the current date range
  const { workouts, loading, error, mutate } = useWorkoutsList({
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    limit: 100, // Maximum allowed by API
  });

  // Convert workouts to calendar events
  const events = useMemo(() => {
    return workouts.map(workoutToCalendarEvent);
  }, [workouts]);

  // Filter events based on visible colors
  const visibleEvents = useMemo(() => {
    return events.filter((event) => isColorVisible(event.color));
  }, [events, isColorVisible]);

  const { leftSidebarOpen, rightSidebarOpen } = useSidebar();

  const handleWorkoutUpdate = async (updatedEvent: CalendarEvent) => {
    try {
      if (!updatedEvent.workoutId) {
        console.error('Cannot update workout without workoutId');
        return;
      }

      // Check for workout time conflicts before updating (exclude current workout)
      const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
        updatedEvent.start,
        updatedEvent.end,
        workouts,
        updatedEvent.workoutId // Exclude the current workout from conflict check
      );

      if (hasTimeConflict && conflictingWorkouts.length > 0) {
        const conflictingWorkout = conflictingWorkouts[0]!; // Safe because we checked length > 0
        const conflictTime = new Date(conflictingWorkout.startTime).toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        });
        toast.error(`Time slot conflicts with existing "${conflictingWorkout.title}" session at ${conflictTime}`);
        return;
      }

      // Convert calendar event to workout update data
      const updateData = {
        title: updatedEvent.title,
        description: updatedEvent.description || '',
        startTime: updatedEvent.start.toISOString(),
        endTime: updatedEvent.end.toISOString(),
        location: updatedEvent.location || '',
        minParticipants: updatedEvent.minParticipants,
        maxParticipants: updatedEvent.maxParticipants,
        status: updatedEvent.status,
      };

      const updatedWorkout = await updateWorkout(updatedEvent.workoutId, updateData);
      if (updatedWorkout) {
        toast.success('Workout updated successfully');
        // Refresh the workout list
        mutate();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update workout';
      toast.error(errorMessage);
    }
  };

  const handleWorkoutDeleteFromCalendar = async (eventId: string) => {
    try {
      // Find the workout ID from the event
      const event = events.find((e) => e.id === eventId);
      if (!event?.workoutId) {
        console.error('Cannot delete workout without workoutId');
        return;
      }

      await deleteWorkout(event.workoutId);
      toast.success('Workout deleted successfully');
      // Refresh the workout list
      mutate();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete workout';
      toast.error(errorMessage);
    }
  };

  const handleWorkoutClick = (event: CalendarEvent) => {
    if (event.workoutId) {
      setSelectedWorkoutId(event.workoutId);
      setWorkoutModalOpen(true);
    }
  };

  const handleWorkoutEdit = (workout: any) => {
    setEditWorkout(workout);
    setWorkoutEditModalOpen(true);
    setWorkoutModalOpen(false);
  };

  const handleWorkoutDelete = async (workoutId: string) => {
    try {
      await deleteWorkout(workoutId);
      toast.success('Workout deleted successfully');
      setWorkoutModalOpen(false);
      mutate(); // Refresh the workout list
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete workout';
      toast.error(errorMessage);
    }
  };

  const handleWorkoutCreate = async (startTime: Date) => {
    try {
      // Create new workout at the clicked time
      const workoutStart = new Date(startTime);
      const workoutEnd = new Date(workoutStart);
      workoutEnd.setHours(workoutStart.getHours() + 1); // 1-hour duration

      // Check for workout time conflicts before creating new workout
      const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
        workoutStart,
        workoutEnd,
        workouts
      );

      if (hasTimeConflict && conflictingWorkouts.length > 0) {
        toast.error('Cannot create workout: time slot conflicts with existing workout');
        return;
      }

      // Create new workout
      const newWorkout = await createWorkout({
        title: `Training Session`,
        description: '',
        startTime: workoutStart.toISOString(),
        endTime: workoutEnd.toISOString(),
        location: '',
        minParticipants: 3,
        maxParticipants: 5,
      });

      if (newWorkout) {
        toast.success('Workout created successfully');
        // Refresh the workout list
        mutate();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create workout';
      toast.error(errorMessage);
    }
  };

  const handleCustomerDrop = async (customer: CustomerResponse, targetDate: Date, targetTime?: Date) => {
    try {
      // Check if customer has credits
      if (customer.sessionCredits <= 0) {
        toast.error('Customer has no credits available');
        return;
      }

      // Calculate target workout time
      const targetDateTime = targetTime || targetDate;
      const workoutStart = new Date(targetDateTime);
      const workoutEnd = new Date(workoutStart);
      workoutEnd.setHours(workoutStart.getHours() + 1); // 1-hour duration

      // Check for customer conflicts
      const { hasConflict, conflictingWorkout } = hasCustomerConflict(customer.id, workoutStart, workoutEnd, workouts);

      if (hasConflict && conflictingWorkout) {
        toast.error(formatConflictMessage(customer.name, conflictingWorkout));
        return;
      }

      // Find if there's an existing workout that overlaps with the target time
      const existingWorkout = workouts.find((workout) => {
        const workoutStartTime = new Date(workout.startTime);
        const workoutEndTime = new Date(workout.endTime);

        // Check if the target time falls within the workout's time range
        // Target time should be >= workout start and < workout end
        return targetDateTime >= workoutStartTime && targetDateTime < workoutEndTime;
      });

      if (existingWorkout) {
        // Check if workout is at capacity
        const currentParticipants = existingWorkout.participantCount || 0;
        const maxParticipants = existingWorkout.maxParticipants || 5;

        if (currentParticipants >= maxParticipants) {
          toast.error('Workout is at maximum capacity');
          return;
        }

        // Add customer to existing workout
        const newParticipant = await addParticipant(existingWorkout.id, { customerId: customer.id });
        if (newParticipant) {
          toast.success(`${customer.name} added to ${existingWorkout.title}`);
        }
      } else {
        // Check for workout time conflicts before creating new workout
        const { hasConflict: hasTimeConflict, conflictingWorkouts } = hasWorkoutTimeConflict(
          workoutStart,
          workoutEnd,
          workouts
        );

        if (hasTimeConflict && conflictingWorkouts.length > 0) {
          const conflictingWorkout = conflictingWorkouts[0]!; // Safe because we checked length > 0
          const conflictTime = new Date(conflictingWorkout.startTime).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          });
          toast.error(
            `Cannot create workout: time slot conflicts with existing "${conflictingWorkout.title}" session at ${conflictTime}`
          );
          return;
        }

        // Create new workout and add customer
        const startTime = new Date(targetDateTime);
        const endTime = new Date(startTime);
        endTime.setHours(startTime.getHours() + 1); // 1-hour duration

        const newWorkout = await createWorkout({
          title: `Training Session`,
          description: '',
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          location: '',
          minParticipants: 3,
          maxParticipants: 5,
        });

        // Add customer to the new workout
        if (newWorkout) {
          const newParticipant = await addParticipant(newWorkout.id, { customerId: customer.id });
          if (newParticipant) {
            toast.success(`${customer.name} added to new workout`);
          }
        }
      }

      // Refresh the workout list
      mutate();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add customer to workout';
      toast.error(errorMessage);
    }
  };

  if (error) {
    return (
      <div className="flex-1 min-w-0">
        <div
          className="flex has-data-[slot=month-view]:flex-1 flex-col rounded-lg"
          style={
            {
              '--event-height': `${EventHeight}px`,
              '--event-gap': `${EventGap}px`,
              '--week-cells-height': `${WeekCellsHeight}px`,
            } as React.CSSProperties
          }
        >
          <div className={cn('flex flex-col sm:flex-row sm:items-center justify-between gap-2 py-5 sm:px-4')}>
            <div className="flex max-sm:items-center justify-between gap-1.5">
              <SidebarTrigger
                data-state={leftSidebarOpen ? 'invisible' : 'visible'}
                className="peer size-7 text-muted-foreground/80 hover:text-foreground/80 hover:bg-transparent! sm:-ms-1.5 lg:data-[state=invisible]:opacity-0 lg:data-[state=invisible]:pointer-events-none transition-opacity ease-in-out duration-200"
                isOutsideSidebar
              />

              <SidebarTrigger
                data-state={rightSidebarOpen ? 'invisible' : 'visible'}
                className="peer size-7 text-muted-foreground/80 hover:text-foreground/80 hover:bg-transparent! sm:-ms-1.5 lg:data-[state=invisible]:opacity-0 lg:data-[state=invisible]:pointer-events-none transition-opacity ease-in-out duration-200"
                isOutsideSidebar
                direction="right"
              />
            </div>
          </div>

          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-500 mb-2">Error loading workouts</p>
              <p className="text-sm text-muted-foreground">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <CalendarDndProvider onEventUpdate={handleWorkoutUpdate} onCustomerDrop={handleCustomerDrop}>
      <div className="flex-1 min-w-0">
        <EventCalendar
          events={visibleEvents}
          onEventCreate={handleWorkoutCreate}
          onEventUpdate={handleWorkoutUpdate}
          onEventDelete={handleWorkoutDeleteFromCalendar}
          onEventClick={handleWorkoutClick}
          initialView="week"
        />
      </div>

      {/* Workout Details Modal */}
      <WorkoutDetailsModal
        workoutId={selectedWorkoutId}
        open={workoutModalOpen}
        onOpenChange={setWorkoutModalOpen}
        onEdit={handleWorkoutEdit}
        onDelete={handleWorkoutDelete}
        onWorkoutUpdated={() => mutate()} // Pass callback to refresh workout list when workout is updated
        refreshTrigger={refreshTrigger}
      />

      {/* Workout Edit Modal */}
      <WorkoutEditModal
        workout={editWorkout}
        open={workoutEditModalOpen}
        onOpenChange={setWorkoutEditModalOpen}
        onSuccess={() => {
          mutate(); // Refresh workout list
          setEditWorkout(null);
          setRefreshTrigger((prev) => prev + 1); // Trigger refresh of workout details modal
        }}
      />
    </CalendarDndProvider>
  );
}
